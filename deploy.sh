#!/bin/bash

# ZFMI 智能部署脚本
# 自动处理Docker容器部署（使用应用层加密）

set -e

# 默认配置
CONTAINER_NAME="zfmi"
IMAGE_NAME="jhxxr/zfmi:latest"
API_SECRET="jhxnb666"
MIHOMO_SECRET="jhxnb666"
HTTP_PORT="8888"
FORCE_RECREATE="false"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${CYAN}========================================${NC}"
    echo -e "${CYAN}      网络管理系统部署脚本${NC}"
    echo -e "${CYAN}========================================${NC}"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -f, --force             强制重新创建容器"
    echo "  -n, --name NAME         容器名称 (默认: zfmi)"
    echo "  -i, --image IMAGE       镜像名称 (默认: jhxxr/zfmi:latest)"
    echo "  -k, --api-key KEY       API密钥 (默认: jhxnb666)"
    echo "  -s, --service-key KEY   服务密钥 (默认: jhxnb666)"
    echo "  --http-port PORT        HTTP端口 (默认: 8888)"
    echo ""
    echo "示例:"
    echo "  $0                      # 基础部署"
    echo "  $0 -f                  # 强制重新创建容器"
    echo "  $0 -k mykey123         # 使用自定义API密钥"
    echo ""
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE_RECREATE="true"
                shift
                ;;
            -n|--name)
                CONTAINER_NAME="$2"
                shift 2
                ;;
            -i|--image)
                IMAGE_NAME="$2"
                shift 2
                ;;
            -k|--api-key)
                API_SECRET="$2"
                shift 2
                ;;
            -s|--service-key)
                MIHOMO_SECRET="$2"
                shift 2
                ;;
            --http-port)
                HTTP_PORT="$2"
                shift 2
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    print_success "Docker检查通过"
}

# 检查系统环境
check_system() {
    print_info "检查系统环境..."
    
    # 检查TUN模块
    if ! lsmod | grep -q tun; then
        print_warning "TUN模块未加载，尝试加载..."
        if sudo modprobe tun 2>/dev/null; then
            print_success "TUN模块加载成功"
        else
            print_warning "TUN模块加载失败，可能影响TUN模式功能"
        fi
    else
        print_success "TUN模块已加载"
    fi
    
    # 检查TUN设备
    if [ ! -c /dev/net/tun ]; then
        print_warning "TUN设备不存在，尝试创建..."
        sudo mkdir -p /dev/net
        sudo mknod /dev/net/tun c 10 200 2>/dev/null || true
    fi
    
    # 启用IP转发
    if [ "$(cat /proc/sys/net/ipv4/ip_forward)" != "1" ]; then
        print_info "启用IPv4转发..."
        echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward >/dev/null
    fi
    
    print_success "系统环境检查完成"
}

# 移除 SSL 相关功能，使用应用层加密

# 停止并删除现有容器
cleanup_container() {
    if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        print_info "停止现有容器: ${CONTAINER_NAME}"
        docker stop "${CONTAINER_NAME}" >/dev/null 2>&1 || true
        
        print_info "删除现有容器: ${CONTAINER_NAME}"
        docker rm "${CONTAINER_NAME}" >/dev/null 2>&1 || true
        
        print_success "容器清理完成"
    fi
}

# 拉取最新镜像
pull_image() {
    print_info "拉取最新镜像: ${IMAGE_NAME}"
    docker pull "${IMAGE_NAME}"
    print_success "镜像拉取完成"
}

# 构建Docker运行命令
build_docker_command() {
    local cmd="docker run -d"
    cmd+=" --name ${CONTAINER_NAME}"
    cmd+=" --network host"
    cmd+=" --restart unless-stopped"
    cmd+=" --cap-add NET_ADMIN"
    cmd+=" --cap-add NET_RAW"
    cmd+=" --device /dev/net/tun:/dev/net/tun"
    cmd+=" --env TZ=Asia/Shanghai"
    cmd+=" --env API_SECRET=${API_SECRET}"
    cmd+=" --env MIHOMO_SECRET=${MIHOMO_SECRET}"
    cmd+=" --env SUPERVISOR_ENABLED=true"
    cmd+=" --env MIHOMO_LOG_LEVEL=info"
    cmd+=" --env LOG_LEVEL=info"
    cmd+=" --env API_PORT=${HTTP_PORT}"

    # 系统挂载
    cmd+=" --volume /lib/modules:/lib/modules:ro"
    cmd+=" --volume /etc/resolv.conf:/etc/resolv.conf:ro"

    # 镜像名称
    cmd+=" ${IMAGE_NAME}"

    echo "$cmd"
}

# 部署容器
deploy_container() {
    print_info "部署容器: ${CONTAINER_NAME}"

    local docker_cmd=$(build_docker_command)

    if eval "$docker_cmd"; then
        print_success "容器部署成功"

        # 等待容器启动
        print_info "等待容器启动..."
        sleep 5

        # 检查容器状态
        if docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
            print_success "容器运行正常"
            show_access_info
        else
            print_error "容器启动失败"
            print_info "查看日志: docker logs ${CONTAINER_NAME}"
            exit 1
        fi
    else
        print_error "容器部署失败"
        exit 1
    fi
}

# 显示访问信息
show_access_info() {
    local local_ip=$(hostname -I | awk '{print $1}' || echo "your-ip")

    echo ""
    print_success "部署完成！"
    echo ""
    echo -e "${CYAN}访问信息:${NC}"
    echo -e "  管理 API:   ${GREEN}http://${local_ip}:${HTTP_PORT}/api/${NC}"
    echo -e "  服务 API:   ${GREEN}http://${local_ip}:11024/${NC}"
    echo -e "  网络端口:   ${GREEN}${local_ip}:10801${NC}"
    echo ""
    echo -e "${CYAN}安全说明:${NC}"
    echo -e "  ${YELLOW}使用应用层加密保证数据传输安全${NC}"
    echo -e "  ${YELLOW}敏感数据在传输过程中已加密处理${NC}"
    echo ""
    echo -e "${CYAN}管理命令:${NC}"
    echo -e "  查看日志: ${BLUE}docker logs -f ${CONTAINER_NAME}${NC}"
    echo -e "  停止服务: ${BLUE}docker stop ${CONTAINER_NAME}${NC}"
    echo -e "  重启服务: ${BLUE}docker restart ${CONTAINER_NAME}${NC}"
    echo ""
}

# 主函数
main() {
    print_header
    echo ""

    # 解析命令行参数
    parse_args "$@"

    # 显示配置信息
    print_info "部署配置:"
    echo "  容器名称: ${CONTAINER_NAME}"
    echo "  镜像名称: ${IMAGE_NAME}"
    echo "  HTTP端口: ${HTTP_PORT}"
    echo "  安全模式: 应用层加密"
    echo ""

    # 检查环境
    check_docker
    check_system

    # 清理现有容器
    if [ "$FORCE_RECREATE" = "true" ] || docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        cleanup_container
    fi

    # 拉取镜像
    pull_image

    # 部署容器
    deploy_container
}

# 运行主函数
main "$@"
